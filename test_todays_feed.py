#!/usr/bin/env python3
"""
Test script for Today's Feed API endpoint
"""
import requests
import json
import sys
from typing import Dict, Any

# Test data - sample posts similar to the ones in the response file
TEST_POSTS = [
    {
        "activity_urn": "urn:li:activity:test1",
        "text": "The biggest announcements yet - Google is significantly accelerating its investments in Pakistan! This week alongside Ministry of IT and Telecommunication Pakistan and other key stakeholders, we announced: AI Leaders Fellowship for top 100 organizations in Pakistan",
        "total_reactions": 26,
        "total_comments": 1,
        "total_shares": 2,
        "author_urn": "urn:li:member:594922129"
    },
    {
        "activity_urn": "urn:li:activity:test2", 
        "text": "I kicked out content creators from my office... Here's the complete story. Problem: Our office was small and the social media team (Content creators) used to make so much noise and keep distracting everyone.",
        "total_reactions": 505,
        "total_comments": 64,
        "total_shares": 4,
        "author_urn": "urn:li:member:939553365"
    },
    {
        "activity_urn": "urn:li:activity:test3",
        "text": "Help Needed from Mathematicians. Is there any mathematician or have masters or higher level of education in mathematics who can help me to convert a hypergeometric function 3F_2(a,b,c,alpha,beta,z) into a Meijer G function?",
        "total_reactions": 27,
        "total_comments": 10,
        "total_shares": 0,
        "author_urn": "urn:li:member:174837325"
    },
    {
        "activity_urn": "urn:li:activity:test4",
        "text": "Most founders do not fear numbers. They fear what the numbers might tell them. It is easier to stay busy than to open your P&L and face the truth about margins, costs, and cash.",
        "total_reactions": 15,
        "total_comments": 1,
        "total_shares": 2,
        "author_urn": "urn:li:member:574818853"
    }
]

def test_todays_feed_api(base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """Test the Today's Feed API endpoint"""
    
    # Test payload
    payload = {
        "posts": TEST_POSTS,
        "general_persona_keywords": ["backend engineer", "software developer", "C++", "Go", "API design"],
        "content_persona_keywords": ["system architecture", "performance optimization", "cloud computing"],
        "network_persona_keywords": ["software engineers", "tech leads", "system architects"]
    }
    
    try:
        print("Testing Today's Feed API...")
        print(f"URL: {base_url}/todays-feed")
        print(f"Number of test posts: {len(TEST_POSTS)}")
        
        response = requests.post(
            f"{base_url}/todays-feed",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful")
            
            # Analyze the response
            analyze_response(result)
            
            # Save response for analysis
            with open("test_response.json", "w") as f:
                json.dump(result, f, indent=2)
            print("📁 Response saved to test_response.json")
            
            return result
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None

def analyze_response(response: Dict[str, Any]) -> None:
    """Analyze the API response for quality issues"""

    if not response.get("success"):
        print("❌ Response indicates failure")
        return

    categories = response.get("categories", [])
    print(f"\n📊 Response Analysis:")
    print(f"Number of categories: {len(categories)}")

    total_posts = 0
    quality_issues = []

    for i, category in enumerate(categories):
        cat_name = category.get("category_name", "Unknown")
        sub_categories = category.get("sub_categories", [])

        print(f"\n{i+1}. Category: {cat_name}")
        print(f"   Sub-categories: {len(sub_categories)}")

        for j, subcat in enumerate(sub_categories):
            subcat_name = subcat.get("sub_categories_name", "Unknown")
            posts = subcat.get("posts", [])
            total_posts += len(posts)

            print(f"   {j+1}. {subcat_name} ({len(posts)} posts)")

            # Check for obvious mismatches
            if len(posts) > 0:
                first_post_text = posts[0].get("text", "")[:100]
                print(f"      Sample post: {first_post_text}...")

                # Quality checks
                if "mathematical" in first_post_text.lower() and "financial" in subcat_name.lower():
                    quality_issues.append(f"Math post in Financial category: {subcat_name}")
                if "office" in first_post_text.lower() and "payment" in subcat_name.lower():
                    quality_issues.append(f"Office story in Payments category: {subcat_name}")
                if "kicked out" in first_post_text.lower() and any(tech in subcat_name.lower() for tech in ["backend", "api", "tech", "engineering"]):
                    quality_issues.append(f"Office management story in tech category: {subcat_name}")

    print(f"\nTotal posts in response: {total_posts}")
    print(f"Original posts sent: {len(TEST_POSTS)}")

    if quality_issues:
        print(f"\n⚠️  Quality Issues Found:")
        for issue in quality_issues:
            print(f"   - {issue}")
    else:
        print(f"\n✅ No obvious quality issues detected")

if __name__ == "__main__":
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    test_todays_feed_api(base_url)
